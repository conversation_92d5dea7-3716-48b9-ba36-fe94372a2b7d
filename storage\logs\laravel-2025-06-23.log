[2025-06-23 01:05:49] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'edu_db2', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-06-23 01:05:49] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'edu_db2', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-06-23 01:05:49] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'edu_db2', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-06-23 01:05:58] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'edu_db2', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-06-23 01:05:59] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'edu_db2', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-06-23 01:05:59] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'edu_db2', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-06-23 01:06:07] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'edu_db2', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-06-23 01:06:07] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'edu_db2', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-06-23 01:06:16] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'edu_db2' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'edu_db2', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'edu_db2', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(83): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-06-23 05:48:21] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'teacher_id' in 'field list' (Connection: mysql, SQL: update `teaching_schedules` set `start_time` = 2025-06-24 09:00:00, `end_time` = 2025-06-24 10:30:00, `teacher_id` = 3, `teaching_schedules`.`updated_at` = 2025-06-23 05:48:21 where `id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'teacher_id' in 'field list' (Connection: mysql, SQL: update `teaching_schedules` set `start_time` = 2025-06-24 09:00:00, `end_time` = 2025-06-24 10:30:00, `teacher_id` = 3, `teaching_schedules`.`updated_at` = 2025-06-23 05:48:21 where `id` = 1) at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('update `teachin...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(584): Illuminate\\Database\\Connection->run('update `teachin...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(536): Illuminate\\Database\\Connection->affectingStatement('update `teachin...', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3865): Illuminate\\Database\\Connection->update('update `teachin...', Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1115): Illuminate\\Database\\Query\\Builder->update(Object(Illuminate\\Support\\Collection))
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Builder->update(Array)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1155): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1013): Illuminate\\Database\\Eloquent\\Model->save(Array)
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php(494): Illuminate\\Database\\Eloquent\\Model->update(Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable->App\\Filament\\Resources\\TaskResource\\Pages\\{closure}(Array)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\actions\\src\\MountableAction.php(41): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php(98): Filament\\Actions\\MountableAction->call(Array)
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Pages\\BasePage->callMountedAction(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(474): Livewire\\Wrapped->__call('callMountedActi...', Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\ClearTeamSettingsCache.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ClearTeamSettingsCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Projects\\Web\\htdocs\\edu-v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 C:\\Projects\\Web\\htdocs\\edu-v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#67 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'teacher_id' in 'field list' at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:592)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(592): PDO->prepare('update `teachin...')
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('update `teachin...', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('update `teachin...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(584): Illuminate\\Database\\Connection->run('update `teachin...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(536): Illuminate\\Database\\Connection->affectingStatement('update `teachin...', Array)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3865): Illuminate\\Database\\Connection->update('update `teachin...', Array)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1115): Illuminate\\Database\\Query\\Builder->update(Object(Illuminate\\Support\\Collection))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Builder->update(Array)
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1155): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1013): Illuminate\\Database\\Eloquent\\Model->save(Array)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php(494): Illuminate\\Database\\Eloquent\\Model->update(Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable->App\\Filament\\Resources\\TaskResource\\Pages\\{closure}(Array)
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\actions\\src\\MountableAction.php(41): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php(98): Filament\\Actions\\MountableAction->call(Array)
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Pages\\BasePage->callMountedAction(Array)
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(474): Livewire\\Wrapped->__call('callMountedActi...', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\ClearTeamSettingsCache.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ClearTeamSettingsCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 C:\\Projects\\Web\\htdocs\\edu-v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 C:\\Projects\\Web\\htdocs\\edu-v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#69 {main}
"} 
[2025-06-23 05:55:49] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-23 08:00:00","new_start":"2025-06-23 07:00:00","old_end":"2025-06-23 09:00:00","new_end":"2025-06-23 08:00:00"} 
[2025-06-23 06:21:00] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-23 07:00:00","new_start":"2025-06-24 07:00:00","old_end":"2025-06-23 08:00:00","new_end":"2025-06-24 08:00:00"} 
[2025-06-23 07:56:21] local.INFO: Updating task {"task_id":8,"old_start":"2025-06-23 14:02:00","new_start":"2025-06-24 13:00:00","old_end":"2025-06-23 18:02:00","new_end":"2025-06-24 17:00:00"} 
[2025-06-23 08:18:20] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-24 07:00:00","new_start":"2025-06-25 07:00:00","old_end":"2025-06-24 08:00:00","new_end":"2025-06-25 08:00:00"} 
[2025-06-23 15:51:06] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-25 07:00:00","new_start":"2025-06-26 07:00:00","old_end":"2025-06-25 08:00:00","new_end":"2025-06-26 08:00:00"} 
[2025-06-23 15:51:09] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-26 07:00:00","new_start":"2025-06-25 07:00:00","old_end":"2025-06-26 08:00:00","new_end":"2025-06-25 08:00:00"} 
[2025-06-23 15:51:14] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-25 07:00:00","new_start":"2025-06-26 07:00:00","old_end":"2025-06-25 08:00:00","new_end":"2025-06-26 08:00:00"} 
[2025-06-23 15:51:24] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-26 07:00:00","new_start":"2025-06-25 07:00:00","old_end":"2025-06-26 08:00:00","new_end":"2025-06-25 08:00:00"} 
[2025-06-23 15:51:27] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-25 07:00:00","new_start":"2025-06-26 07:00:00","old_end":"2025-06-25 08:00:00","new_end":"2025-06-26 08:00:00"} 
[2025-06-23 15:53:43] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-26 07:00:00","new_start":"2025-06-25 07:00:00","old_end":"2025-06-26 08:00:00","new_end":"2025-06-25 08:00:00"} 
[2025-06-23 15:54:26] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-25 07:00:00","new_start":"2025-06-24 07:00:00","old_end":"2025-06-25 08:00:00","new_end":"2025-06-24 08:00:00"} 
[2025-06-23 15:54:31] local.INFO: Updating task {"task_id":7,"old_start":"2025-06-24 07:00:00","new_start":"2025-06-25 07:00:00","old_end":"2025-06-24 08:00:00","new_end":"2025-06-25 08:00:00"} 
