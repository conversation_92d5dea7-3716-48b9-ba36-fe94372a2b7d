{"__meta": {"id": "Xe9100e555fbe3b0d5ffb58e946ece709", "datetime": "2025-06-23 04:24:29", "utime": 1750652669.200236, "method": "GET", "uri": "/livewire/livewire.js?id=38dc8241", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750652668.4527, "end": 1750652669.20026, "duration": 0.7475600242614746, "duration_str": "748ms", "measures": [{"label": "Booting", "start": 1750652668.4527, "relative_start": 0, "end": 1750652668.785528, "relative_end": 1750652668.785528, "duration": 0.3328280448913574, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750652668.785541, "relative_start": 0.332841157913208, "end": 1750652669.200262, "relative_end": 2.1457672119140625e-06, "duration": 0.4147210121154785, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49009608, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/livewire/livewire.js", "status_code": "<pre class=sf-dump id=sf-dump-2027813161 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2027813161\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/javascript; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-178592925 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">38dc8241</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-178592925\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1522140624 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1522140624\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1086263151 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/backend/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"719 characters\">XSRF-TOKEN=eyJpdiI6ImJKcXlyNEFvQk81VXFDSytKakJEbHc9PSIsInZhbHVlIjoiT25tdE1xekphWGtQWFJrSlMrOWdlVFpTTytsS0t5RzlTMXRBZkNaWW5ZYnVwM3NNR1lnemdsVjVNMGlsWVRYUTNDcVJTaTNuMTlVN0dIL1ZEaVlJcVBVRDdLTEpEdkd0aUJDT2ZxUi9vNXIvei8rNk5tMTZjT09ocWMyY3VUZmoiLCJtYWMiOiJjNzUxODcyYmUyMTM4Mjg0MGUxOWE5ZTBmZDg5NjE5ODQyZTMyZTQ4OTczYTExOTdhMDE1MDhkNmQzZGUyZjg0IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6ImlxK3JMajVUVFdxcElVV2dIUVQ4RUE9PSIsInZhbHVlIjoiYkJFdFBrVyt4ZEN1T3NFYThLZGI0TllxVFBhYXFBVGhKcjlOK3htODdXcituUGZ0ZTlaMDF5NUY1S00xL05kdVV0U1g0TExGSDJJWVlQRjZPWkQ5ZmZRcVREb1VWT2Evdm9VODY2QzhFR1hnem9GbFlJTWRXTGwrZ2VlZTZWa28iLCJtYWMiOiIwNzk2ZjFiNDQxZjU2ZDFkZTE0NWI2NWIyYWZlNmM5MzRlYTcxNjNkOWUwNDgyZGQ4NTUwODMzNWI5YTdiYTBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086263151\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1958033979 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImJKcXlyNEFvQk81VXFDSytKakJEbHc9PSIsInZhbHVlIjoiT25tdE1xekphWGtQWFJrSlMrOWdlVFpTTytsS0t5RzlTMXRBZkNaWW5ZYnVwM3NNR1lnemdsVjVNMGlsWVRYUTNDcVJTaTNuMTlVN0dIL1ZEaVlJcVBVRDdLTEpEdkd0aUJDT2ZxUi9vNXIvei8rNk5tMTZjT09ocWMyY3VUZmoiLCJtYWMiOiJjNzUxODcyYmUyMTM4Mjg0MGUxOWE5ZTBmZDg5NjE5ODQyZTMyZTQ4OTczYTExOTdhMDE1MDhkNmQzZGUyZjg0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImlxK3JMajVUVFdxcElVV2dIUVQ4RUE9PSIsInZhbHVlIjoiYkJFdFBrVyt4ZEN1T3NFYThLZGI0TllxVFBhYXFBVGhKcjlOK3htODdXcituUGZ0ZTlaMDF5NUY1S00xL05kdVV0U1g0TExGSDJJWVlQRjZPWkQ5ZmZRcVREb1VWT2Evdm9VODY2QzhFR1hnem9GbFlJTWRXTGwrZ2VlZTZWa28iLCJtYWMiOiIwNzk2ZjFiNDQxZjU2ZDFkZTE0NWI2NWIyYWZlNmM5MzRlYTcxNjNkOWUwNDgyZGQ4NTUwODMzNWI5YTdiYTBhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958033979\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-84585337 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 23 Jun 2026 04:24:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Oct 2024 19:35:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 23 Jun 2025 04:24:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">340160</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84585337\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-800046244 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-800046244\", {\"maxDepth\":0})</script>\n"}}