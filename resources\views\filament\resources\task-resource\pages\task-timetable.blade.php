<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Timetable Controls -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <!-- Date Navigation -->
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-2">
                    <button
                        wire:click="changeDate('{{ now()->parse($selectedDate ?? now())->subDay()->format('Y-m-d') }}')"
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    >
                        <x-heroicon-o-chevron-left class="w-4 h-4" />
                    </button>

                    <input
                        type="date"
                        wire:model.live="selectedDate"
                        class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />

                    <button
                        wire:click="changeDate('{{ now()->parse($selectedDate ?? now())->addDay()->format('Y-m-d') }}')"
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    >
                        <x-heroicon-o-chevron-right class="w-4 h-4" />
                    </button>
                </div>

                <button
                    wire:click="changeDate('{{ now()->format('Y-m-d') }}')"
                    class="px-3 py-2 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                >
                    Today
                </button>
            </div>

            <!-- View Mode Toggle -->
            <div class="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                <button
                    wire:click="changeViewMode('day')"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $viewMode === 'day' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Day
                </button>
                <button
                    wire:click="changeViewMode('week')"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $viewMode === 'week' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Week
                </button>
                <button
                    wire:click="changeViewMode('month')"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $viewMode === 'month' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Month
                </button>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <div class="flex items-center gap-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Show:</span>

                <button
                    wire:click="toggleMyTasks"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $showMyTasks ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    My Tasks
                </button>

                <button
                    wire:click="toggleAssignedToMe"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $showAssignedToMe ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Assigned to Me
                </button>

                <button
                    wire:click="toggleSchedules"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $showSchedules ? 'bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Teaching Schedules
                </button>
            </div>

            <div class="flex items-center gap-2">
                <button
                    wire:click="showAll"
                    class="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                    Show All
                </button>
            </div>
        </div>

        <!-- Legend -->
        {{-- <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Legend</h3>
            <div class="flex flex-wrap gap-4">
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-blue-500 rounded"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Tasks</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-green-500 rounded"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Teaching Schedules</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-red-500 rounded"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">High Priority</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-yellow-500 rounded"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Medium Priority</span>
                </div>
            </div>
        </div> --}}

        <!-- Timetable Container -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
            <div id="timetable-container" class="relative">
                @if($viewMode === 'week')
                    @include('filament.resources.task-resource.pages.partials.week-view', $this->getViewData())
                @elseif($viewMode === 'day')
                    @include('filament.resources.task-resource.pages.partials.day-view', $this->getViewData())
                @else
                    @include('filament.resources.task-resource.pages.partials.month-view', $this->getViewData())
                @endif
            </div>
        </div>

        <!-- Task/Schedule Popover -->
        <div id="task-popover" class="fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 p-3 hidden">
            <div class="flex items-center gap-2">
                <!-- Status Update Button -->
                <button
                    onclick="quickComplete()"
                    class="p-2 rounded-lg bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 transition-colors"
                    title="Update status">
                    <x-heroicon-o-arrow-path class="w-4 h-4" />
                </button>

                <!-- Edit Button -->
                <button
                    onclick="quickEdit()"
                    class="p-2 rounded-lg bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 transition-colors"
                    title="Edit">
                    <x-heroicon-o-pencil class="w-4 h-4" />
                </button>

                <!-- Delete Button -->
                <button
                    onclick="quickDelete()"
                    class="p-2 rounded-lg bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-600 dark:text-red-400 transition-colors"
                    title="Delete">
                    <x-heroicon-o-trash class="w-4 h-4" />
                </button>
            </div>
        </div>

        <!-- Help Button -->
        <div class="fixed bottom-6 left-6 z-50">
            <button
                type="button"
                onclick="toggleHelp()"
                class="bg-gray-600 hover:bg-gray-700 text-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200"
                title="Show keyboard shortcuts">
                <x-heroicon-o-question-mark-circle class="w-5 h-5" />
            </button>
        </div>

        <!-- Help Modal -->
        <div id="help-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Keyboard Shortcuts</h3>
                    <button onclick="toggleHelp()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <x-heroicon-o-x-mark class="w-5 h-5" />
                    </button>
                </div>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">New Task</span>
                        <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl+N</kbd>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">New Schedule</span>
                        <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl+Shift+N</kbd>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Quick Add</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">Click empty slot</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">More Options</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">Right-click empty slot</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Quick Actions</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">Click task/schedule</span>
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-2 pl-4">
                        • Status: Pending/In Progress/Completed/Cancelled<br>
                        • Edit: Open pre-filled edit form<br>
                        • Delete: Remove with confirmation
                    </div>
                </div>
            </div>
        </div>


    </div>

    @push('styles')
        <!-- Font Awesome for icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- SweetAlert2 Custom Styling -->
        <style>
        .swal2-popup {
            border-radius: 0.75rem !important;
            font-family: 'Inter', sans-serif !important;
        }

        .swal2-title {
            font-size: 1.25rem !important;
            font-weight: 600 !important;
        }

        .swal2-content {
            font-size: 0.875rem !important;
            color: #6B7280 !important;
        }

        .swal2-confirm {
            border-radius: 0.5rem !important;
            font-weight: 500 !important;
            padding: 0.5rem 1rem !important;
        }

        .swal2-cancel {
            border-radius: 0.5rem !important;
            font-weight: 500 !important;
            padding: 0.5rem 1rem !important;
        }

        .swal2-icon {
            border: none !important;
        }

        .swal2-icon.swal2-success {
            color: #10B981 !important;
        }

        .swal2-icon.swal2-warning {
            color: #F59E0B !important;
        }

        .swal2-icon.swal2-question {
            color: #3B82F6 !important;
        }

        /* Custom SweetAlert2 buttons */
        .swal-custom-html {
            padding: 0 20px !important;
        }

        .swal-custom-btn {
            width: 100%;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .swal-btn-success {
            background-color: #10B981;
            color: white;
        }

        .swal-btn-success:hover {
            background-color: #059669;
            transform: translateY(-1px);
        }

        .swal-btn-warning {
            background-color: #F59E0B;
            color: white;
        }

        .swal-btn-warning:hover {
            background-color: #D97706;
            transform: translateY(-1px);
        }

        .swal-btn-info {
            background-color: #3B82F6;
            color: white;
        }

        .swal-btn-info:hover {
            background-color: #2563EB;
            transform: translateY(-1px);
        }

        .swal-btn-danger {
            background-color: #EF4444;
            color: white;
        }

        .swal-btn-danger:hover {
            background-color: #DC2626;
            transform: translateY(-1px);
        }
        </style>
    @endpush

    @push('scripts')
        <!-- SweetAlert2 CDN -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <!-- Drag and Drop JavaScript -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeTimetable();

            // Configure SweetAlert2 defaults
            Swal.mixin({
                customClass: {
                    confirmButton: 'swal2-confirm',
                    cancelButton: 'swal2-cancel'
                },
                buttonsStyling: false,
                showClass: {
                    popup: 'animate__animated animate__fadeInDown animate__faster'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp animate__faster'
                }
            });
        });

        function initializeTimetable() {
            // Initialize drag and drop functionality
            const draggableItems = document.querySelectorAll('.draggable-item');
            const dropZones = document.querySelectorAll('.drop-zone');

            draggableItems.forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
                item.addEventListener('dragend', handleDragEnd);
            });

            dropZones.forEach(zone => {
                zone.addEventListener('dragover', handleDragOver);
                zone.addEventListener('drop', handleDrop);
                zone.addEventListener('dragenter', handleDragEnter);
                zone.addEventListener('dragleave', handleDragLeave);
            });
        }

        function handleDragStart(e) {
            const itemId = e.target.dataset.id;
            const itemType = e.target.dataset.type;

            // Debug log
            console.log('Drag start:', { itemId, itemType, target: e.target });

            if (!itemId || !itemType) {
                console.error('Missing drag data:', { itemId, itemType });
                e.preventDefault();
                return;
            }

            e.dataTransfer.setData('text/plain', itemId);
            e.dataTransfer.setData('type', itemType);
            e.target.style.opacity = '0.5';
        }

        function handleDragEnd(e) {
            e.target.style.opacity = '1';
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDragEnter(e) {
            e.preventDefault();
            e.target.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.target.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.target.classList.remove('drag-over');

            const itemId = e.dataTransfer.getData('text/plain');
            const itemType = e.dataTransfer.getData('type');

            // Find the drop zone (might be a child element)
            let dropZone = e.target;
            while (dropZone && !dropZone.classList.contains('drop-zone')) {
                dropZone = dropZone.parentElement;
            }

            const newDateTime = dropZone ? dropZone.dataset.datetime : e.target.dataset.datetime;

            // Debug log the drop data
            console.log('Drop operation data:', {
                itemId, itemType, newDateTime,
                dropTarget: e.target,
                dropZone: dropZone,
                datasetDatetime: e.target.dataset.datetime,
                dropZoneDatetime: dropZone?.dataset.datetime
            });

            // Validate that we have all required data
            if (!itemId || !itemType) {
                console.error('Missing required data for drop operation', {
                    itemId, itemType, newDateTime,
                    dataTransferData: {
                        textPlain: e.dataTransfer.getData('text/plain'),
                        type: e.dataTransfer.getData('type')
                    }
                });
                return;
            }

            // If no datetime found, try to construct one from the current date and hour
            if (!newDateTime) {
                console.warn('No datetime found in drop zone, attempting to construct one');
                const currentDate = new Date();
                const dateStr = currentDate.toISOString().split('T')[0];
                const hour = Math.floor(currentDate.getHours());
                newDateTime = `${dateStr} ${hour.toString().padStart(2, '0')}:00:00`;
                console.log('Constructed datetime:', newDateTime);
            }

            // Show loading state
            const draggedElement = document.querySelector(`[data-id="${itemId}"]`);
            if (draggedElement) {
                draggedElement.style.opacity = '0.5';
                draggedElement.style.pointerEvents = 'none';
            }

            if (itemType === 'task') {
                @this.call('updateTaskPosition', itemId, newDateTime)
                    .then(() => {
                        console.log('Task moved successfully');
                        // Reset element state
                        if (draggedElement) {
                            draggedElement.style.opacity = '1';
                            draggedElement.style.pointerEvents = 'auto';
                        }
                    })
                    .catch((error) => {
                        console.error('Failed to move task:', error);
                        // Reset element state
                        if (draggedElement) {
                            draggedElement.style.opacity = '1';
                            draggedElement.style.pointerEvents = 'auto';
                        }
                    });
            } else if (itemType === 'schedule') {
                @this.call('updateSchedulePosition', itemId, newDateTime)
                    .then(() => {
                        console.log('Schedule moved successfully');
                        // Reset element state
                        if (draggedElement) {
                            draggedElement.style.opacity = '1';
                            draggedElement.style.pointerEvents = 'auto';
                        }
                    })
                    .catch((error) => {
                        console.error('Failed to move schedule:', error);
                        // Reset element state
                        if (draggedElement) {
                            draggedElement.style.opacity = '1';
                            draggedElement.style.pointerEvents = 'auto';
                        }
                    });
            }
        }

        // Reinitialize after Livewire updates
        document.addEventListener('livewire:navigated', function() {
            initializeTimetable();
        });

        document.addEventListener('livewire:init', () => {
            initializeTimetable();
        });

        // Listen for Livewire events
        window.addEventListener('task-moved', event => {
            console.log('Task moved:', event.detail);
            // You can add a toast notification here
        });

        window.addEventListener('schedule-moved', event => {
            console.log('Schedule moved:', event.detail);
            // You can add a toast notification here
        });

        // Quick task creation function
        function createTaskAtTime(datetime) {
            // Use $wire directly
            @this.mountAction('new_task', {
                start_datetime: datetime
            });
            
        }

        // Quick schedule creation function
        function createScheduleAtTime(datetime) {
            // Use $wire directly
            @this.mountAction('new_schedule', {
                start_time: datetime
            });
        }

        // Context menu functionality
        function showContextMenu(event, datetime) {
            event.preventDefault();

            // Remove any existing context menu
            const existingMenu = document.getElementById('context-menu');
            if (existingMenu) {
                existingMenu.remove();
            }

            // Create context menu
            const menu = document.createElement('div');
            menu.id = 'context-menu';
            menu.className = 'fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 py-2 min-w-48';
            menu.style.left = event.pageX + 'px';
            menu.style.top = event.pageY + 'px';

            menu.innerHTML = `
                <button onclick="createTaskAtTime('${datetime}'); closeContextMenu();"
                        class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Task
                </button>
                <button onclick="createScheduleAtTime('${datetime}'); closeContextMenu();"
                        class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                    </svg>
                    Add Teaching Schedule
                </button>
            `;

            document.body.appendChild(menu);

            // Close menu when clicking outside
            setTimeout(() => {
                document.addEventListener('click', closeContextMenu);
            }, 100);
        }

        function closeContextMenu() {
            const menu = document.getElementById('context-menu');
            if (menu) {
                menu.remove();
            }
            document.removeEventListener('click', closeContextMenu);
        }

        // Help modal functionality
        function toggleHelp() {
            const modal = document.getElementById('help-modal');
            modal.classList.toggle('hidden');
        }

        // Close help modal when clicking outside
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                toggleHelp();
            }
        });

        // Task/Schedule popover functionality
        let popoverData = {
            show: false,
            currentTaskId: null,
            currentTaskType: null,
            position: { x: 0, y: 0 }
        };

        let popoverTimeout = null;

        window.showTaskPopover = function(event, taskId, taskType) {
            // Clear any existing timeout
            if (popoverTimeout) {
                clearTimeout(popoverTimeout);
            }

            // Set a small delay to allow drag operations to start
            popoverTimeout = setTimeout(() => {
                event.stopPropagation();
                event.preventDefault();

                // Set the task data
                popoverData.currentTaskId = taskId;
                popoverData.currentTaskType = taskType;

            // Position the popover
            const rect = event.target.getBoundingClientRect();
            const popoverWidth = 150;
            const popoverHeight = 60;

            let left = rect.right + 10;
            let top = rect.top;

            // Adjust if popover would go off-screen
            if (left + popoverWidth > window.innerWidth) {
                left = rect.left - popoverWidth - 10;
            }

            if (top + popoverHeight > window.innerHeight) {
                top = rect.bottom - popoverHeight;
            }

            if (top < 0) {
                top = rect.bottom + 10;
            }

            // Update popover position and show it
            const popover = document.getElementById('task-popover');
            if (popover) {
                popover.style.left = left + 'px';
                popover.style.top = top + 'px';
                popover.classList.remove('hidden');

                // Update button visibility
                updatePopoverButtons(taskType);

                // Close on click outside
                setTimeout(() => {
                    document.addEventListener('click', closeTaskPopover);
                }, 100);
            }
            }, 150); // 150ms delay to allow drag to start
        };

        // Cancel popover when drag starts
        document.addEventListener('dragstart', function() {
            if (popoverTimeout) {
                clearTimeout(popoverTimeout);
                popoverTimeout = null;
            }
            closeTaskPopover();
        });

        function updatePopoverButtons(taskType) {
            const taskButtons = document.querySelectorAll('[data-task-type="task"]');
            const scheduleButtons = document.querySelectorAll('[data-task-type="schedule"]');

            taskButtons.forEach(btn => {
                btn.style.display = taskType === 'task' ? 'block' : 'none';
            });

            scheduleButtons.forEach(btn => {
                btn.style.display = taskType === 'schedule' ? 'block' : 'none';
            });
        }

        function closeTaskPopover() {
            const popover = document.getElementById('task-popover');
            if (popover) {
                popover.classList.add('hidden');
            }
            document.removeEventListener('click', closeTaskPopover);
            popoverData.show = false;
        }

        // Quick action functions
        window.quickComplete = function(itemId,itemType) {
            // if (!popoverData.currentTaskId || !popoverData.currentTaskType) return;

            // const itemType = popoverData.currentTaskType;
            // const itemId = popoverData.currentTaskId;

            Swal.fire({
                title: `Update ${itemType} status`,
                html: `
                    <div class="text-gray-600 mb-6">Choose the new status for this ${itemType}:</div>
                    <div class="grid grid-cols-2 gap-3">
                        <br />
                        <button id="btn-pending" class="swal-custom-btn swal-btn-info">
                            <i class="fas fa-hourglass-start mr-2"></i>&nbsp;
                            Pending
                        </button>
                        <button id="btn-in-progress" class="swal-custom-btn swal-btn-warning">
                            <i class="fas fa-clock mr-2"></i>&nbsp;
                            In Progress
                        </button>
                        <button id="btn-complete" class="swal-custom-btn swal-btn-success">
                            <i class="fas fa-check mr-2"></i>&nbsp;
                            Completed
                        </button>
                        <button id="btn-cancelled" class="swal-custom-btn swal-btn-danger">
                            <i class="fas fa-times mr-2"></i>&nbsp;
                            Cancelled
                        </button>
                    </div>
                `,
                showCancelButton: true,
                showConfirmButton: false,
                cancelButtonText: '<i class="fas fa-times"></i> Cancel',
                cancelButtonColor: '#6B7280',
                customClass: {
                    htmlContainer: 'swal-custom-html'
                },
                didOpen: () => {
                    // Add event listeners to custom buttons
                    document.getElementById('btn-pending').addEventListener('click', () => {
                        Swal.close();
                        updateTaskStatus(itemType, itemId, 'pending', 'Pending');
                    });

                    document.getElementById('btn-in-progress').addEventListener('click', () => {
                        Swal.close();
                        updateTaskStatus(itemType, itemId, 'in_progress', 'In Progress');
                    });

                    document.getElementById('btn-complete').addEventListener('click', () => {
                        Swal.close();
                        updateTaskStatus(itemType, itemId, 'completed', 'Completed');
                    });

                    document.getElementById('btn-cancelled').addEventListener('click', () => {
                        Swal.close();
                        updateTaskStatus(itemType, itemId, 'cancelled', 'Cancelled');
                    });
                }
            });

            closeTaskPopover();
        };

        function updateTaskStatus(itemType, itemId, status, statusLabel) {
            if (itemType === 'task') {
                @this.call('quickUpdateTaskStatus', itemId, status);
            } else {
                @this.call('quickUpdateScheduleStatus', itemId, status);
            }

            Swal.fire({
                title: `${statusLabel}!`,
                text: `Your ${itemType} has been marked as ${statusLabel.toLowerCase()}.`,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }

        window.quickEdit = function(itemId,itemType) {
            // if (!popoverData.currentTaskId || !popoverData.currentTaskType) return;

            // const itemType = popoverData.currentTaskType;
            // const itemId = popoverData.currentTaskId;

            closeTaskPopover();

            // Call the edit action with the task/schedule data
            if (itemType === 'task') {
                @this.call('quickEditTask', itemId);
            } else {
                @this.call('quickEditSchedule', itemId);
            }
        };

        window.quickDelete = function(itemId,itemType) {
            // if (!popoverData.currentTaskId || !popoverData.currentTaskType) return;

            // const itemType = popoverData.currentTaskType;
            // const itemId = popoverData.currentTaskId;

            Swal.fire({
                title: `Delete ${itemType}?`,
                text: `This action cannot be undone!`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#EF4444',
                cancelButtonColor: '#6B7280',
                confirmButtonText: '<i class="fas fa-trash"></i> Yes, delete it!',
                cancelButtonText: '<i class="fas fa-times"></i> Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    if (itemType === 'task') {
                        @this.call('quickDeleteTask', itemId);
                    } else {
                        @this.call('quickDeleteSchedule', itemId);
                    }

                    Swal.fire({
                        title: 'Deleted!',
                        text: `Your ${itemType} has been deleted.`,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                }
            });

            closeTaskPopover();
        };

        // Close help modal when clicking outside
        // document.getElementById('help-modal').addEventListener('click', function(e) {
        // document.addEventListener('click', function(e) {
        //     if (event.target.matches(".quickComplete")) {
        //         // Your code here
        //         console.log("quickComplete clicked");
        //         // window.quickComplete(event);
        //     }else if (event.target.matches(".quickEdit")) {
        //         // Your code here
        //         console.log("quickEdit clicked");
        //         // window.quickEdit(event);
        //     }else if (event.target.matches(".quickDelete")) {
        //         // Your code here
        //         console.log("quickDelete clicked");
        //         // window.quickDelete(event);
        //     }
        // });
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + N for new task
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                @this.mountAction('new_task');
            }

            // Ctrl/Cmd + Shift + N for new schedule
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'N') {
                e.preventDefault();
                @this.mountAction('new_schedule');
            }
        });


        </script>
    @endpush

    @push('styles')
        <style>
        .task-item {
            width: 100%; 
        }

        .draggable-item {
            cursor: move;
            transition: opacity 0.2s ease, transform 0.2s ease;
        }

        .draggable-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .drop-zone {
            min-height: 60px;
            transition: background-color 0.2s ease;
        }

        .drop-zone.drag-over {
            background-color: rgba(59, 130, 246, 0.1);
            border: 2px dashed #3B82F6;
        }

        .time-slot {
            border-bottom: 1px solid #e5e7eb;
        }

        .time-slot:last-child {
            border-bottom: none;
        }

        .timetable-grid {
            display: grid;
            grid-template-columns: 80px repeat(7, 1fr);
            gap: 1px;
            background-color: #e5e7eb;
        }

        .timetable-cell {
            background-color: white;
            min-height: 60px;
            padding: 4px;
            position: relative;
            overflow-x: hidden;
        }

        .dark .timetable-cell {
            background-color: #374151;
        }

        .dark .timetable-grid {
            background-color: #4b5563;
        }

        .task-item > div {
            border-radius: 4px;
            padding: 4px 8px;
            margin: 1px 0;
            font-size: 12px;
            line-height: 1.3;
            border-left: 3px solid;
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(4px);
            position: relative;
            overflow: hidden;
        }

        .task-item > div::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.05));
        }

        .task-item > div.priority-high {
            border-left-color: #EF4444;
            background-color: rgba(239, 68, 68, 0.1);
        }

        .task-item > div.priority-urgent {
            border-left-color: #DC2626;
            background-color: rgba(220, 38, 38, 0.15);
        }

        .task-item > div.priority-medium {
            border-left-color: #F59E0B;
            background-color: rgba(245, 158, 11, 0.1);
        }

        .task-item > div.priority-low {
            border-left-color: #10B981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        .schedule-item {
            border-left-color: #10B981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        .calendar-day {
            min-height: 120px;
            border: 1px solid #e5e7eb;
            padding: 8px;
        }

        .calendar-day.today {
            background-color: rgba(59, 130, 246, 0.05);
            border-color: #3B82F6;
        }

        .calendar-day.other-month {
            opacity: 0.5;
        }

        .day-view-hour {
            border-bottom: 1px solid #e5e7eb;
            min-height: 80px;
        }

        .current-time-indicator {
            position: absolute;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #EF4444;
            z-index: 10;
        }

        #task-popover {
            animation: popoverFadeIn 0.15s ease-out;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        @keyframes popoverFadeIn {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-5px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .draggable-item:hover {
            z-index: 5;
            filter: brightness(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .draggable-item {
            transition: all 0.15s ease;
        }

        /* Hide edit action buttons */
        .hidden-edit-action {
            display: none !important;
        }


        </style>
    @endpush
</x-filament-panels::page>
