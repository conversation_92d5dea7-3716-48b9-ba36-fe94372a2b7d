<?php
    $startOfWeek = now()->parse($selectedDate)->startOfWeek();
    $days = collect(range(0, 6))->map(fn($i) => $startOfWeek->copy()->addDays($i));
    $hours = range(6, 22); // 6 AM to 10 PM
    $currentHour = now()->hour;
    $currentDay = now()->format('Y-m-d');
?>

<div class="timetable-grid">
    <!-- Header Row -->
    <div class="timetable-cell font-medium text-center bg-gray-50 dark:bg-gray-700 sticky top-0 z-20">
        <div class="text-sm font-semibold">Time</div>
    </div>
    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="timetable-cell font-medium text-center bg-gray-50 dark:bg-gray-700 sticky top-0 z-20 <?php echo e($day->format('Y-m-d') === $currentDay ? 'bg-blue-50 dark:bg-blue-900' : ''); ?>">
            <div class="text-sm font-semibold <?php echo e($day->format('Y-m-d') === $currentDay ? 'text-blue-600 dark:text-blue-400' : ''); ?>">
                <?php echo e($day->format('D')); ?>

            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
                <?php echo e($day->format('M j')); ?>

            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Time Slots -->
    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $hours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!-- Time Label -->
        <div class="timetable-cell font-medium text-center bg-gray-50 dark:bg-gray-700 flex items-center justify-center sticky left-0 z-10">
            <span class="text-sm <?php echo e($hour === $currentHour ? 'text-blue-600 dark:text-blue-400 font-bold' : ''); ?>">
                <?php echo e(sprintf('%02d:00', $hour)); ?>

            </span>
        </div>

        <!-- Day Columns -->
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $cellDateTime = $day->copy()->setHour($hour)->setMinute(0)->setSecond(0);
                $cellDateTimeString = $cellDateTime->format('Y-m-d H:i:s');
                $isCurrentHour = $day->format('Y-m-d') === $currentDay && $hour === $currentHour;

                // Get tasks for this time slot and sort by start time
                $cellTasks = $tasks->filter(function($task) use ($cellDateTime) {
                    return $task->start_datetime->format('Y-m-d H') === $cellDateTime->format('Y-m-d H');
                })->sortBy('start_datetime');

                // Get teaching schedules for this time slot and sort by start time
                $cellSchedules = $teachingSchedules->filter(function($schedule) use ($cellDateTime) {
                    return $schedule->start_time->format('Y-m-d H') === $cellDateTime->format('Y-m-d H');
                })->sortBy('start_time');
            ?>

            <div class="timetable-cell drop-zone relative <?php echo e($isCurrentHour ? 'bg-blue-50 dark:bg-blue-900 ring-1 ring-blue-200 dark:ring-blue-700' : ''); ?>"
                 data-datetime="<?php echo e($cellDateTimeString); ?>">

                <!-- Current time indicator -->
                <!--[if BLOCK]><![endif]--><?php if($isCurrentHour): ?>
                    <div class="current-time-indicator" style="top: <?php echo e((now()->minute / 60) * 100); ?>%"></div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!-- Tasks -->
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cellTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                
                <div class="task-item relative inline-block">
                    <div
                        class="draggable-item priority-<?php echo e($task->priority); ?> cursor-pointer"
                        draggable="true"
                        data-id="<?php echo e($task->id); ?>"
                        data-type="task"
                        
                        
                        @click="showPopover=!showPopover"
                    >
                        <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                            <?php echo e($task->title); ?>

                        </div>
                        <div class="text-gray-600 dark:text-gray-400 flex items-center gap-1 text-xs">
                            <span class="font-mono font-medium"><?php echo e($task->start_datetime->format('H:i')); ?></span>
                            <!--[if BLOCK]><![endif]--><?php if($task->end_datetime): ?>
                                <span class="text-gray-500">-<?php echo e($task->end_datetime->format('H:i')); ?></span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php if($task->priority === 'high' || $task->priority === 'urgent'): ?>
                                ‼️ 
                                
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php if($task->has_alert): ?>
                                🔔
                                
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <!--[if BLOCK]><![endif]--><?php if($task->assignedUser): ?>
                            <div class="text-gray-500 dark:text-gray-400 text-xs truncate">
                                Assigned to: <?php echo e($task->assignedUser->name); ?>

                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($task->user && $task->assigned_to && $task->user_id !== $task->assigned_to): ?>
                            <div class="text-gray-500 dark:text-gray-400 text-xs truncate">
                                By: <?php echo e($task->user->name); ?>

                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                    
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                <!-- Teaching Schedules -->
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cellSchedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                <div x-data="{showPopover:false,taskType:'schedule',
                taskID:<?php echo \Illuminate\Support\Js::from($schedule->id)->toHtml() ?>,
                taskTitle:<?php echo \Illuminate\Support\Js::from($schedule->subject->name)->toHtml() ?>,
                taskLoc:<?php echo \Illuminate\Support\Js::from($schedule->classroom->room_name)->toHtml() ?>,
                taskData:<?php echo \Illuminate\Support\Js::from($schedule)->toHtml() ?>}" 
                class="task-item relative inline-block"> 
                    <div
                        class="draggable-item schedule-item cursor-pointer"
                        draggable="true"
                        data-id="<?php echo e($schedule->id); ?>"
                        data-type="schedule"
                        
                        
                        @click="showPopover=!showPopover"
                    >
                        <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                            <?php echo e($schedule->subject->name); ?>

                        </div>
                        <div class="text-gray-600 dark:text-gray-400 text-xs">
                            <span class="font-mono font-medium"><?php echo e($schedule->start_time->format('H:i')); ?></span>
                            <!--[if BLOCK]><![endif]--><?php if($schedule->end_time): ?>
                                <span class="text-gray-500">-<?php echo e($schedule->end_time->format('H:i')); ?></span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <div class="text-gray-500 dark:text-gray-400 text-xs truncate">
                            (<?php echo e($schedule->classroom->room_number); ?>)
                            <?php echo e($schedule->classroom->room_name); ?>

                        </div>
                    </div>
                    <?php echo $__env->make("filament.resources.task-resource.pages.partials.popover", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                <!-- Empty state for drop zone -->
                <!--[if BLOCK]><![endif]--><?php if($cellTasks->isEmpty() && $cellSchedules->isEmpty()): ?>
                    <div class="h-full flex items-center justify-center text-gray-400 dark:text-gray-600 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 rounded group relative"
                         
                         oncontextmenu="showContextMenu(event, '<?php echo e($cellDateTimeString); ?>')"
                         onclick="showContextMenu(event, '<?php echo e($cellDateTimeString); ?>')"
                         title="Left click: Add Task | Right click: More options">
                        <div class="text-center">
                            <span class="text-xs opacity-0 group-hover:opacity-100 transition-opacity font-medium">+ Add Task</span>
                            <div class="text-xs opacity-0 group-hover:opacity-100 transition-opacity text-gray-500 dark:text-gray-400 mt-1">
                                <?php echo e($cellDateTime->format('H:i')); ?>

                            </div>
                        </div>
                        <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <span class="text-xs">⋮</span>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH C:\Projects\Web\htdocs\edu-v2\resources\views/filament/resources/task-resource/pages/partials/week-view.blade.php ENDPATH**/ ?>