{"__meta": {"id": "X2407ce3ee96127f79dd05c3a77931ea6", "datetime": "2025-06-23 04:24:27", "utime": 1750652667.225358, "method": "GET", "uri": "/backend/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750652660.312788, "end": 1750652667.225381, "duration": 6.912592887878418, "duration_str": "6.91s", "measures": [{"label": "Booting", "start": 1750652660.312788, "relative_start": 0, "end": 1750652660.626448, "relative_end": 1750652660.626448, "duration": 0.3136599063873291, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750652660.626455, "relative_start": 0.31366705894470215, "end": 1750652667.225383, "relative_end": 2.1457672119140625e-06, "duration": 6.598927974700928, "duration_str": "6.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET backend/login", "domain": null, "middleware": "panel:backend, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent", "controller": "App\\Filament\\Pages\\Auth\\Login@__invoke", "as": "filament.backend.auth.login", "namespace": null, "prefix": "/backend", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": {"app.filament.pages.auth.login #DS71y3LvRqYVQLQsxljc": "array:4 [\n  \"data\" => array:15 [\n    \"data\" => array:3 [\n      \"email\" => \"<EMAIL>\"\n      \"password\" => \"password\"\n      \"remember\" => true\n    ]\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.pages.auth.login\"\n  \"component\" => \"App\\Filament\\Pages\\Auth\\Login\"\n  \"id\" => \"DS71y3LvRqYVQLQsxljc\"\n]", "filament.livewire.notifications #1a8k4nNUMxi6X8YuG8Yi": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3674\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"1a8k4nNUMxi6X8YuG8Yi\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ECrLc6MubAzfJrIkowA6XmgQsV5DaEBOIjc6pVbu", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/backend/greenwood-elementary/tasks\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backend/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/backend/login", "status_code": "<pre class=sf-dump id=sf-dump-1121772845 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1121772845\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-797454107 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-797454107\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1508866696 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1508866696\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1055726162 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"364 characters\">filament_demo_session=eyJpdiI6IkpseFkyV2tFbGdsa3M4WnZmRDdnVlE9PSIsInZhbHVlIjoiOFVNeXFBNFZTZk5IWkdTNFdZUHBPYlZhMk81bUlWZW15U1c3d2R5NXFWYWxDRkcvQkd0bHdtUGxrS3BobmFCNURCM1VZTnJ5UlVyR3phUmFJbjFKakZQL2UwL2VCa3BoY2ZhQklZTy9ZNjVWUmhxT0JyV2VGNDhOcUEzT3Y1SHAiLCJtYWMiOiI3MDBmNjM2MWUwYzNkNGQ4ZDQ3YjgzMWRmMTU2NjE0YTdhODRjOGU0MzM5ZGVmOWJhNGNhMGY4ZWU2ZDY5NjkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055726162\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-795953274 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dhB3hK4MyCcb7EhWK3CCqy4oAh3oDVKezdtwxgYs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795953274\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2116988739 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 23 Jun 2025 04:24:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImJKcXlyNEFvQk81VXFDSytKakJEbHc9PSIsInZhbHVlIjoiT25tdE1xekphWGtQWFJrSlMrOWdlVFpTTytsS0t5RzlTMXRBZkNaWW5ZYnVwM3NNR1lnemdsVjVNMGlsWVRYUTNDcVJTaTNuMTlVN0dIL1ZEaVlJcVBVRDdLTEpEdkd0aUJDT2ZxUi9vNXIvei8rNk5tMTZjT09ocWMyY3VUZmoiLCJtYWMiOiJjNzUxODcyYmUyMTM4Mjg0MGUxOWE5ZTBmZDg5NjE5ODQyZTMyZTQ4OTczYTExOTdhMDE1MDhkNmQzZGUyZjg0IiwidGFnIjoiIn0%3D; expires=Mon, 23 Jun 2025 06:24:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6ImlxK3JMajVUVFdxcElVV2dIUVQ4RUE9PSIsInZhbHVlIjoiYkJFdFBrVyt4ZEN1T3NFYThLZGI0TllxVFBhYXFBVGhKcjlOK3htODdXcituUGZ0ZTlaMDF5NUY1S00xL05kdVV0U1g0TExGSDJJWVlQRjZPWkQ5ZmZRcVREb1VWT2Evdm9VODY2QzhFR1hnem9GbFlJTWRXTGwrZ2VlZTZWa28iLCJtYWMiOiIwNzk2ZjFiNDQxZjU2ZDFkZTE0NWI2NWIyYWZlNmM5MzRlYTcxNjNkOWUwNDgyZGQ4NTUwODMzNWI5YTdiYTBhIiwidGFnIjoiIn0%3D; expires=Mon, 23 Jun 2025 06:24:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImJKcXlyNEFvQk81VXFDSytKakJEbHc9PSIsInZhbHVlIjoiT25tdE1xekphWGtQWFJrSlMrOWdlVFpTTytsS0t5RzlTMXRBZkNaWW5ZYnVwM3NNR1lnemdsVjVNMGlsWVRYUTNDcVJTaTNuMTlVN0dIL1ZEaVlJcVBVRDdLTEpEdkd0aUJDT2ZxUi9vNXIvei8rNk5tMTZjT09ocWMyY3VUZmoiLCJtYWMiOiJjNzUxODcyYmUyMTM4Mjg0MGUxOWE5ZTBmZDg5NjE5ODQyZTMyZTQ4OTczYTExOTdhMDE1MDhkNmQzZGUyZjg0IiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 06:24:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6ImlxK3JMajVUVFdxcElVV2dIUVQ4RUE9PSIsInZhbHVlIjoiYkJFdFBrVyt4ZEN1T3NFYThLZGI0TllxVFBhYXFBVGhKcjlOK3htODdXcituUGZ0ZTlaMDF5NUY1S00xL05kdVV0U1g0TExGSDJJWVlQRjZPWkQ5ZmZRcVREb1VWT2Evdm9VODY2QzhFR1hnem9GbFlJTWRXTGwrZ2VlZTZWa28iLCJtYWMiOiIwNzk2ZjFiNDQxZjU2ZDFkZTE0NWI2NWIyYWZlNmM5MzRlYTcxNjNkOWUwNDgyZGQ4NTUwODMzNWI5YTdiYTBhIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 06:24:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116988739\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-702454219 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ECrLc6MubAzfJrIkowA6XmgQsV5DaEBOIjc6pVbu</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/backend/greenwood-elementary/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/backend/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702454219\", {\"maxDepth\":0})</script>\n"}}