{"__meta": {"id": "X8902891ce2da95b8a3fedea6342d483c", "datetime": "2025-06-23 04:43:25", "utime": **********.717884, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750653804.837416, "end": **********.717907, "duration": 0.8804910182952881, "duration_str": "880ms", "measures": [{"label": "Booting", "start": 1750653804.837416, "relative_start": 0, "end": **********.191529, "relative_end": **********.191529, "duration": 0.3541131019592285, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.191539, "relative_start": 0.3541231155395508, "end": **********.717909, "relative_end": 2.1457672119140625e-06, "duration": 0.5263700485229492, "duration_str": "526ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49027104, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 40, "nb_visible_statements": 40, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009899999999999999, "accumulated_duration_str": "9.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.603588, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 4.646}, {"sql": "select * from `teams` where `slug` = 'greenwood-elementary' limit 1", "type": "query", "params": [], "bindings": ["greenwood-elementary"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.606499, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 4.646, "width_percent": 2.525}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 188}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 309}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}], "start": **********.611002, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "User.php:188", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=188", "ajax": false, "filename": "User.php", "line": "188"}, "connection": "edu_db2", "explain": null, "start_percent": 7.172, "width_percent": 3.131}, {"sql": "select count(*) as aggregate from `shop_orders` where `team_id` = 1 and `created_at` <= '2025-06-23 04:43:25' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-06-23 04:43:25", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 71}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.62332, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:71", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=71", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "71"}, "connection": "edu_db2", "explain": null, "start_percent": 10.303, "width_percent": 3.232}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where `team_id` = 1 and `created_at` <= '2025-06-23 04:43:25' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-06-23 04:43:25", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 72}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.625244, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:72", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=72", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "72"}, "connection": "edu_db2", "explain": null, "start_percent": 13.535, "width_percent": 2.424}, {"sql": "select count(*) as aggregate from `shop_customers` where `team_id` = 1 and `created_at` <= '2025-06-23 04:43:25' and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-06-23 04:43:25", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 73}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.626953, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:73", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=73", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "73"}, "connection": "edu_db2", "explain": null, "start_percent": 15.96, "width_percent": 2.222}, {"sql": "select count(*) as aggregate from `users` where `team_id` = 1 and `created_at` <= '2025-06-23 04:43:25'", "type": "query", "params": [], "bindings": [1, "2025-06-23 04:43:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.6286159, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=74", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "74"}, "connection": "edu_db2", "explain": null, "start_percent": 18.182, "width_percent": 2.02}, {"sql": "select * from `teams` where `slug` = 'greenwood-elementary' limit 1", "type": "query", "params": [], "bindings": ["greenwood-elementary"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.637751, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 20.202, "width_percent": 3.232}, {"sql": "select count(*) as aggregate from `users` where `team_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.640766, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:31", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=31", "ajax": false, "filename": "TeamStatsWidget.php", "line": "31"}, "connection": "edu_db2", "explain": null, "start_percent": 23.434, "width_percent": 2.222}, {"sql": "select count(*) as aggregate from `shop_customers` where `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.64301, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:32", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=32", "ajax": false, "filename": "TeamStatsWidget.php", "line": "32"}, "connection": "edu_db2", "explain": null, "start_percent": 25.657, "width_percent": 2.222}, {"sql": "select count(*) as aggregate from `shop_products` where `team_id` = 1 and `shop_products`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.645024, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=33", "ajax": false, "filename": "TeamStatsWidget.php", "line": "33"}, "connection": "edu_db2", "explain": null, "start_percent": 27.879, "width_percent": 2.02}, {"sql": "select count(*) as aggregate from `shop_orders` where `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.646704, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:34", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=34", "ajax": false, "filename": "TeamStatsWidget.php", "line": "34"}, "connection": "edu_db2", "explain": null, "start_percent": 29.899, "width_percent": 1.717}, {"sql": "select count(*) as aggregate from `shop_orders` where `team_id` = 1 and `created_at` >= '2025-05-24 04:43:25' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-05-24 04:43:25", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.648468, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:39", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=39", "ajax": false, "filename": "TeamStatsWidget.php", "line": "39"}, "connection": "edu_db2", "explain": null, "start_percent": 31.616, "width_percent": 2.222}, {"sql": "select count(*) as aggregate from `shop_customers` where `team_id` = 1 and `created_at` >= '2025-05-24 04:43:25' and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-05-24 04:43:25", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.650439, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:43", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=43", "ajax": false, "filename": "TeamStatsWidget.php", "line": "43"}, "connection": "edu_db2", "explain": null, "start_percent": 33.838, "width_percent": 2.727}, {"sql": "select * from `teams` where `slug` = 'greenwood-elementary' limit 1", "type": "query", "params": [], "bindings": ["greenwood-elementary"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6561852, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 36.566, "width_percent": 2.828}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6590228, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 39.394, "width_percent": 2.424}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6608799, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 41.818, "width_percent": 2.222}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.662695, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 44.04, "width_percent": 2.121}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6644778, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 46.162, "width_percent": 2.222}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.666339, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 48.384, "width_percent": 2.222}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.66811, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 50.606, "width_percent": 2.222}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6699488, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 52.828, "width_percent": 2.525}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6717699, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 55.354, "width_percent": 2.121}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.673517, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 57.475, "width_percent": 2.121}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.675284, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 59.596, "width_percent": 2.121}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.677056, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 61.717, "width_percent": 3.03}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.678974, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 64.747, "width_percent": 2.626}, {"sql": "select * from `teams` where `slug` = 'greenwood-elementary' limit 1", "type": "query", "params": [], "bindings": ["greenwood-elementary"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.685499, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 67.374, "width_percent": 3.232}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.688277, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 70.606, "width_percent": 3.03}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6902618, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 73.636, "width_percent": 2.323}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6920428, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 75.96, "width_percent": 2.222}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.693813, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 78.182, "width_percent": 1.818}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6955569, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 80, "width_percent": 2.121}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.698391, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 82.121, "width_percent": 3.535}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.700964, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 85.657, "width_percent": 3.03}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.7033188, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 88.687, "width_percent": 2.424}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.705235, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 91.111, "width_percent": 2.525}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.707061, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 93.636, "width_percent": 2.121}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.708826, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 95.758, "width_percent": 1.919}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.710587, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 97.677, "width_percent": 2.323}]}, "models": {"data": {"App\\Models\\Team": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.stats-overview-widget #mjhBwKi9bV3pioHXaoAo": "array:4 [\n  \"data\" => array:1 [\n    \"filters\" => array:3 [\n      \"businessCustomersOnly\" => null\n      \"startDate\" => null\n      \"endDate\" => null\n    ]\n  ]\n  \"name\" => \"app.filament.widgets.stats-overview-widget\"\n  \"component\" => \"App\\Filament\\Widgets\\StatsOverviewWidget\"\n  \"id\" => \"mjhBwKi9bV3pioHXaoAo\"\n]", "app.filament.widgets.team-stats-widget #ExQViqztAAks9yc1yNio": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.team-stats-widget\"\n  \"component\" => \"App\\Filament\\Widgets\\TeamStatsWidget\"\n  \"id\" => \"ExQViqztAAks9yc1yNio\"\n]", "app.filament.widgets.customers-chart #H6YnAy8yZDWkVMhg3ugu": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"79b385326a73164aad8f3fa7b146e2cf\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.customers-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\CustomersChart\"\n  \"id\" => \"H6YnAy8yZDWkVMhg3ugu\"\n]", "app.filament.widgets.orders-chart #CaUnwSTi7virOTsmjIwJ": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"e0750753ca6de9d7a1098e252600ca50\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.orders-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\OrdersChart\"\n  \"id\" => \"CaUnwSTi7virOTsmjIwJ\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IHvBNuFRLFd4Q9o4gddMDYowrHIaZYGJbXDBBhDc", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backend/greenwood-elementary\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$SXu8.9FpVOvFtY/F.MCJnu1Psihu51k3op3dRZ4EDF2zWy9wbAicG", "Dashboard_filters": "array:3 [\n  \"businessCustomersOnly\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1162031110 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1162031110\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1074567523 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1074567523\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-530446776 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IHvBNuFRLFd4Q9o4gddMDYowrHIaZYGJbXDBBhDc</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"403 characters\">{&quot;data&quot;:{&quot;filters&quot;:[{&quot;businessCustomersOnly&quot;:null,&quot;startDate&quot;:null,&quot;endDate&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;mjhBwKi9bV3pioHXaoAo&quot;,&quot;name&quot;:&quot;app.filament.widgets.stats-overview-widget&quot;,&quot;path&quot;:&quot;backend\\/greenwood-elementary&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;props&quot;:[&quot;filters&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;8b01a82a91924b7376826cf9562aaca57b9b7ca4eaad5546e7145d06cc65d766&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"293 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;ExQViqztAAks9yc1yNio&quot;,&quot;name&quot;:&quot;app.filament.widgets.team-stats-widget&quot;,&quot;path&quot;:&quot;backend\\/greenwood-elementary&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;427a35966fe7b19b49f854f238471c1b1fa261a60e5831f6ed1ef8d3db79ce37&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"354 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;79b385326a73164aad8f3fa7b146e2cf&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;H6YnAy8yZDWkVMhg3ugu&quot;,&quot;name&quot;:&quot;app.filament.widgets.customers-chart&quot;,&quot;path&quot;:&quot;backend\\/greenwood-elementary&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;a1d37246ba6ddfd5edd3fbe6312a53a285626f1828fb1a679db8581de6e3f0a4&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"351 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;e0750753ca6de9d7a1098e252600ca50&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;CaUnwSTi7virOTsmjIwJ&quot;,&quot;name&quot;:&quot;app.filament.widgets.orders-chart&quot;,&quot;path&quot;:&quot;backend\\/greenwood-elementary&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;14cfeb276d81f36347b28c67cbfefe313babb634385b2984edee4cede165d812&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-530446776\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-563459656 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1905</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">http://127.0.0.1:8000/backend/greenwood-elementary</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1179 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im5FWmtjbHlaWEVUeUIzNjROQkU2MEE9PSIsInZhbHVlIjoiWm9jOUViSTRyY05QM2tFT0NPSG4wbHJpYkVHMUtkVTNYNThHODdROTJPUWRaL2lSSjZ5Zmk0K0czMkhaYTh0RmJOekxQUmU4ZWVheVltOFpaOHQxY3BhNHBtUThuUGxwN3N3THJMUWxScmlkM25LaFlseGFqWDM1NWEwaTM3Zm5UbmRqK2hlcHBuUVFRaDNVcERsU3I3U2JtaVk3bGxLeWUvay9qU3YyazFJPSIsIm1hYyI6IjEyM2ZiOGUzM2E2MWM0YTM1NjNmMTNiZmQ5OTY1ZTM5ODQzMDBkMjFkZmU5ZWJmMWNkNmI2ODRiZGEyMTFlMmMiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkY3VWZ2YnlTYnhsNm1xM0FpVE9NZVE9PSIsInZhbHVlIjoiWW14MVgyTE1HM0FvSDRqSXNBdlpMbnBlU2E0UTRZQnF5eUM2QVZEM05mZ2FtTStBcXdNWHI5dDU5a1RiYnAxelF1RDNqT0VjaDZTL1VrRkFFeVducURuK05senUzZDUvWTNDUVRUU2pRc296bGh5aWNHdnZYTzJGSndhRFRxZjAiLCJtYWMiOiI4NzMwZTY0YjBmM2RkMmQ1ZDA3Y2QyYzI2MzI5ZGZhNzYyMTU5NDJkNTM2N2RlYWRhYjM0ODk2ZTdhMGQ4M2I5IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6IlJWeVhMTXlCTHhSdEVWcG11bHRKdlE9PSIsInZhbHVlIjoiS1BNY2F0UjFaNTJ5dnA0ZnpOc2kxMUg0dnBkQVNPSmhBeFhnT0FMSTNCYXJNenFkUitYOWFrb3hzU0lTdTlrNmZUdmVDd0F5UTJQSWF1dDRaL0pKL01WczlkN2cwMkpIdTY5UURvQndhSzEyNmZhMzk1aEFIZSt1TnNuaGJ2RG0iLCJtYWMiOiI3NDNhOTdhMGY2N2RhYzAzYmJmNGM4MTYwZDI4ZGMyYTcxNTc3MzgyZDBmYWNhMmVlNDEzOTA2Yjc4MThhZTc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-563459656\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"73 characters\">1|m4kZlBmoOl|$2y$12$SXu8.9FpVOvFtY/F.MCJnu1Psihu51k3op3dRZ4EDF2zWy9wbAicG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IHvBNuFRLFd4Q9o4gddMDYowrHIaZYGJbXDBBhDc</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XpokvQsHwwid42uknVKVSfKTnme3EdU4cqlXhJe1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-642151734 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 23 Jun 2025 04:43:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImF0cUhFbnJBZmpCT3QrN05ac1FLelE9PSIsInZhbHVlIjoiaDlraVkwQy83RG55VnNuSTA1Um15dzZYYlZ1VGtqeW5xZmc3bVBFR0JEMWR6elVjQk16OHRaU2w2UG1XNXEwMGd0UGdiMXpUTDZIWGhhZTlwRlFpZW1IVjl4N3ozdjdrUzZlMENWTEVvQW9jRENvNjhzR2RLN1oxRVRqdTFDU1MiLCJtYWMiOiI2OTRhZDA5ODI1MWFjYTAwMDFlOTcyYjkzNmQ4NGRmNjNlNDkwOTk4MmQwNzdjZWQ1NDY0YmFmYjhjN2NlNzA3IiwidGFnIjoiIn0%3D; expires=Mon, 23 Jun 2025 06:43:25 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6IjltVWREUE1wUWlyMmFaYTBsOWV1Qnc9PSIsInZhbHVlIjoiRmttaTF0Y1NKY3ZDL2FOVm1IbmJIVEIyYzBMMWZiQlFkL3Z3aHpNWUJxR0M1TmFESWs0RVV3Y2wxUjBtRDdrM3hmaUNCMUEzZnJuMGdhb3FqSlM0L3BIVHM4V2hiSkFlTlp4OWFCUDMwWVJIcUxUTlhEWU9FYWZ0RnRQOStwUVAiLCJtYWMiOiIyOWVmODBkM2I3YjE0NTU5NWFmNTVjYzRkNmIwZmU0ZTgxZTg3NGYwNDliODBiMjAyNWNhNWNlMDlhOWM0YmI4IiwidGFnIjoiIn0%3D; expires=Mon, 23 Jun 2025 06:43:25 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImF0cUhFbnJBZmpCT3QrN05ac1FLelE9PSIsInZhbHVlIjoiaDlraVkwQy83RG55VnNuSTA1Um15dzZYYlZ1VGtqeW5xZmc3bVBFR0JEMWR6elVjQk16OHRaU2w2UG1XNXEwMGd0UGdiMXpUTDZIWGhhZTlwRlFpZW1IVjl4N3ozdjdrUzZlMENWTEVvQW9jRENvNjhzR2RLN1oxRVRqdTFDU1MiLCJtYWMiOiI2OTRhZDA5ODI1MWFjYTAwMDFlOTcyYjkzNmQ4NGRmNjNlNDkwOTk4MmQwNzdjZWQ1NDY0YmFmYjhjN2NlNzA3IiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 06:43:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6IjltVWREUE1wUWlyMmFaYTBsOWV1Qnc9PSIsInZhbHVlIjoiRmttaTF0Y1NKY3ZDL2FOVm1IbmJIVEIyYzBMMWZiQlFkL3Z3aHpNWUJxR0M1TmFESWs0RVV3Y2wxUjBtRDdrM3hmaUNCMUEzZnJuMGdhb3FqSlM0L3BIVHM4V2hiSkFlTlp4OWFCUDMwWVJIcUxUTlhEWU9FYWZ0RnRQOStwUVAiLCJtYWMiOiIyOWVmODBkM2I3YjE0NTU5NWFmNTVjYzRkNmIwZmU0ZTgxZTg3NGYwNDliODBiMjAyNWNhNWNlMDlhOWM0YmI4IiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 06:43:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642151734\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-671502743 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IHvBNuFRLFd4Q9o4gddMDYowrHIaZYGJbXDBBhDc</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://127.0.0.1:8000/backend/greenwood-elementary</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$SXu8.9FpVOvFtY/F.MCJnu1Psihu51k3op3dRZ4EDF2zWy9wbAicG</span>\"\n  \"<span class=sf-dump-key>Dashboard_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>businessCustomersOnly</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-671502743\", {\"maxDepth\":0})</script>\n"}}