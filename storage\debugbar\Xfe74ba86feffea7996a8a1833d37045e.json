{"__meta": {"id": "Xfe74ba86feffea7996a8a1833d37045e", "datetime": "2025-06-23 01:51:34", "utime": 1750643494.839005, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750643493.66956, "end": 1750643494.839033, "duration": 1.1694729328155518, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1750643493.66956, "relative_start": 0, "end": 1750643494.162333, "relative_end": 1750643494.162333, "duration": 0.49277305603027344, "duration_str": "493ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750643494.162341, "relative_start": 0.4927811622619629, "end": 1750643494.839035, "relative_end": 2.1457672119140625e-06, "duration": 0.6766939163208008, "duration_str": "677ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49112384, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\HomepageController@index", "namespace": null, "prefix": "", "where": [], "as": "homepage", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FHttp%2FControllers%2FHomepageController.php&line=14\" onclick=\"\">app/Http/Controllers/HomepageController.php:14-17</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rdNtPjggAXE5OaxUqpfvBl3PZgEKjT10zq0b3yT6", "_previous": "array:1 [\n  \"url\" => \"http://d2.demo.com\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-2038187864 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2038187864\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1595319886 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1595319886\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1053021640 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1053021640\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1569022224 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">d2.demo.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569022224\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-956438700 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-956438700\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-808849897 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 23 Jun 2025 01:51:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im1ZY3d5aGVmNmJmUkZQcSszYjlmNVE9PSIsInZhbHVlIjoiUUdvMjdhczVrS01YUE15L3Vlazh2OE42dXQzYUMwMjlKc0wyeFJKd0NlL3FMYW1UVFIrRS9raDU0ZHFYVjNPbmFLMkRxaEp1bUF2QWoxODQyZDRjb1Y1TGlwczU1TnNnbHF6OEdGSmdVSWhhUklhY2owdUQvVFYrb0xnRWNYODciLCJtYWMiOiJlYjNhYzdmMjViMjI2YmQ1YjA4NDY0OGYzMzU2OGI0MTQ2ODM0MGYxYjJhZmNhNTk0ZDAwZTc0YjE2MTkyMzQ3IiwidGFnIjoiIn0%3D; expires=Mon, 23 Jun 2025 03:51:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6IkZ0Q1lKa0xBcVZ1SGVnRXNPZzI2bHc9PSIsInZhbHVlIjoiMVl2dVFjYnZ0Yzl6ZXpsUW83em83Rmo2cVhHZjlBOEwxT2JGS1FlbFdCTXVlNVRsdlhxYWZ6cnhLcWNNcDNMYzFxNWg2OGw3QklVMjBqdTNYbkkxcXBJWmIwNE1XOHFLbTJNSHJodlEzZXFXRGxCbmEwS3BtZ3BTYVIrbGkyMHAiLCJtYWMiOiJmZmJkZDE1YThmMTRiMmI0YTY0NTk0Y2UxODhmZTQ3NDI3NmU0ZjNjMDU2NWEwMjM3YTRkODBlNmQ0NTcwNzU5IiwidGFnIjoiIn0%3D; expires=Mon, 23 Jun 2025 03:51:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im1ZY3d5aGVmNmJmUkZQcSszYjlmNVE9PSIsInZhbHVlIjoiUUdvMjdhczVrS01YUE15L3Vlazh2OE42dXQzYUMwMjlKc0wyeFJKd0NlL3FMYW1UVFIrRS9raDU0ZHFYVjNPbmFLMkRxaEp1bUF2QWoxODQyZDRjb1Y1TGlwczU1TnNnbHF6OEdGSmdVSWhhUklhY2owdUQvVFYrb0xnRWNYODciLCJtYWMiOiJlYjNhYzdmMjViMjI2YmQ1YjA4NDY0OGYzMzU2OGI0MTQ2ODM0MGYxYjJhZmNhNTk0ZDAwZTc0YjE2MTkyMzQ3IiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 03:51:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6IkZ0Q1lKa0xBcVZ1SGVnRXNPZzI2bHc9PSIsInZhbHVlIjoiMVl2dVFjYnZ0Yzl6ZXpsUW83em83Rmo2cVhHZjlBOEwxT2JGS1FlbFdCTXVlNVRsdlhxYWZ6cnhLcWNNcDNMYzFxNWg2OGw3QklVMjBqdTNYbkkxcXBJWmIwNE1XOHFLbTJNSHJodlEzZXFXRGxCbmEwS3BtZ3BTYVIrbGkyMHAiLCJtYWMiOiJmZmJkZDE1YThmMTRiMmI0YTY0NTk0Y2UxODhmZTQ3NDI3NmU0ZjNjMDU2NWEwMjM3YTRkODBlNmQ0NTcwNzU5IiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 03:51:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808849897\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-683103015 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rdNtPjggAXE5OaxUqpfvBl3PZgEKjT10zq0b3yT6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"18 characters\">http://d2.demo.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683103015\", {\"maxDepth\":0})</script>\n"}}