{"__meta": {"id": "X9a0f387d927e81257ac137fe6b594014", "datetime": "2025-06-23 05:48:25", "utime": **********.006565, "method": "GET", "uri": "/_ignition/health-check", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.997845, "end": **********.006589, "duration": 1.0087440013885498, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": **********.997845, "relative_start": 0, "end": **********.342011, "relative_end": **********.342011, "duration": 0.3441660404205322, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.342024, "relative_start": 0.3441791534423828, "end": **********.006592, "relative_end": 3.0994415283203125e-06, "duration": 0.6645679473876953, "duration_str": "665ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54446240, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET _ignition/health-check", "middleware": "Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled", "uses": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController@__invoke", "controller": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "as": "ignition.healthCheck", "namespace": null, "prefix": "_ignition", "where": []}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/_ignition/health-check", "status_code": "<pre class=sf-dump id=sf-dump-185959397 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-185959397\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-106154052 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/backend/greenwood-elementary/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1179 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im5FWmtjbHlaWEVUeUIzNjROQkU2MEE9PSIsInZhbHVlIjoiWm9jOUViSTRyY05QM2tFT0NPSG4wbHJpYkVHMUtkVTNYNThHODdROTJPUWRaL2lSSjZ5Zmk0K0czMkhaYTh0RmJOekxQUmU4ZWVheVltOFpaOHQxY3BhNHBtUThuUGxwN3N3THJMUWxScmlkM25LaFlseGFqWDM1NWEwaTM3Zm5UbmRqK2hlcHBuUVFRaDNVcERsU3I3U2JtaVk3bGxLeWUvay9qU3YyazFJPSIsIm1hYyI6IjEyM2ZiOGUzM2E2MWM0YTM1NjNmMTNiZmQ5OTY1ZTM5ODQzMDBkMjFkZmU5ZWJmMWNkNmI2ODRiZGEyMTFlMmMiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6Im1qVm9lb3p5S1lCTzZUdGdtblpqa1E9PSIsInZhbHVlIjoiRHFJUDdkRmtSU3dnbnVENjNqaDF2UGpXd2o4OHdhOWZybmpJMVhLQlRlV3BnYURTUkpEMnVOVGY5U1NLcDZGc0pKOVZ5cjNKNzVBOStKNXQvSE1leGE4TTY4a0NOVkY4MXpOcFNDeUFjaWdzZjFiNFRJcnRUeEs0WEVNbDlTbmUiLCJtYWMiOiJkZTBkYTUyNWI5ZWQ1Njc0ZmJmNjEzNTZlOWIxNmIzMTE0MDBhZGJlNGI5NGI0NzlhZDZiNDEyNzExODQwNWJiIiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6IkRucWZxT3VJZGc5TEo2ZzZBVUY1cEE9PSIsInZhbHVlIjoiN0wyS1NYM2gvTE1oK2lsOE1EcFZFbUxyejdDcDlSeEJ4aTJVMW5IZlZ2aWZNaHdBejc4ZXljbzdQN1RlN0NJUWlBNHBMbEYvSFQzOWZ2RnVoQkZhaUdiZzlYblNOZ002Nzc5aHpzejhWOEw0clE4bWUzNmRPTXg2VE0ycHo1c2IiLCJtYWMiOiJjYWYyNzBlYWM4ZjgyNzlmODY1NzgxMmYxYWNhYTJkMTA4YjM0NmRmZWE5ZDUwNTdmZjRjY2YzMTdjYmI4YTk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106154052\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1204322260 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"400 characters\">eyJpdiI6Im5FWmtjbHlaWEVUeUIzNjROQkU2MEE9PSIsInZhbHVlIjoiWm9jOUViSTRyY05QM2tFT0NPSG4wbHJpYkVHMUtkVTNYNThHODdROTJPUWRaL2lSSjZ5Zmk0K0czMkhaYTh0RmJOekxQUmU4ZWVheVltOFpaOHQxY3BhNHBtUThuUGxwN3N3THJMUWxScmlkM25LaFlseGFqWDM1NWEwaTM3Zm5UbmRqK2hlcHBuUVFRaDNVcERsU3I3U2JtaVk3bGxLeWUvay9qU3YyazFJPSIsIm1hYyI6IjEyM2ZiOGUzM2E2MWM0YTM1NjNmMTNiZmQ5OTY1ZTM5ODQzMDBkMjFkZmU5ZWJmMWNkNmI2ODRiZGEyMTFlMmMiLCJ0YWciOiIifQ==</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im1qVm9lb3p5S1lCTzZUdGdtblpqa1E9PSIsInZhbHVlIjoiRHFJUDdkRmtSU3dnbnVENjNqaDF2UGpXd2o4OHdhOWZybmpJMVhLQlRlV3BnYURTUkpEMnVOVGY5U1NLcDZGc0pKOVZ5cjNKNzVBOStKNXQvSE1leGE4TTY4a0NOVkY4MXpOcFNDeUFjaWdzZjFiNFRJcnRUeEs0WEVNbDlTbmUiLCJtYWMiOiJkZTBkYTUyNWI5ZWQ1Njc0ZmJmNjEzNTZlOWIxNmIzMTE0MDBhZGJlNGI5NGI0NzlhZDZiNDEyNzExODQwNWJiIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkRucWZxT3VJZGc5TEo2ZzZBVUY1cEE9PSIsInZhbHVlIjoiN0wyS1NYM2gvTE1oK2lsOE1EcFZFbUxyejdDcDlSeEJ4aTJVMW5IZlZ2aWZNaHdBejc4ZXljbzdQN1RlN0NJUWlBNHBMbEYvSFQzOWZ2RnVoQkZhaUdiZzlYblNOZ002Nzc5aHpzejhWOEw0clE4bWUzNmRPTXg2VE0ycHo1c2IiLCJtYWMiOiJjYWYyNzBlYWM4ZjgyNzlmODY1NzgxMmYxYWNhYTJkMTA4YjM0NmRmZWE5ZDUwNTdmZjRjY2YzMTdjYmI4YTk4IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204322260\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1996028882 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 23 Jun 2025 05:48:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996028882\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1275812038 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1275812038\", {\"maxDepth\":0})</script>\n"}}