@php
    $startOfWeek = now()->parse($selectedDate)->startOfWeek();
    $days = collect(range(0, 6))->map(fn($i) => $startOfWeek->copy()->addDays($i));
    $hours = range(6, 22); // 6 AM to 10 PM
    $currentHour = now()->hour;
    $currentDay = now()->format('Y-m-d');
@endphp

<div class="timetable-grid">
    <!-- Header Row -->
    <div class="timetable-cell font-medium text-center bg-gray-50 dark:bg-gray-700 sticky top-0 z-20">
        <div class="text-sm font-semibold">Time</div>
    </div>
    @foreach($days as $day)
        <div class="timetable-cell font-medium text-center bg-gray-50 dark:bg-gray-700 sticky top-0 z-20 {{ $day->format('Y-m-d') === $currentDay ? 'bg-blue-50 dark:bg-blue-900' : '' }}">
            <div class="text-sm font-semibold {{ $day->format('Y-m-d') === $currentDay ? 'text-blue-600 dark:text-blue-400' : '' }}">
                {{ $day->format('D') }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ $day->format('M j') }}
            </div>
        </div>
    @endforeach

    <!-- Time Slots -->
    @foreach($hours as $hour)
        <!-- Time Label -->
        <div class="timetable-cell font-medium text-center bg-gray-50 dark:bg-gray-700 flex items-center justify-center sticky left-0 z-10">
            <span class="text-sm {{ $hour === $currentHour ? 'text-blue-600 dark:text-blue-400 font-bold' : '' }}">
                {{ sprintf('%02d:00', $hour) }}
            </span>
        </div>

        <!-- Day Columns -->
        @foreach($days as $day)
            @php
                $cellDateTime = $day->copy()->setHour($hour)->setMinute(0)->setSecond(0);
                $cellDateTimeString = $cellDateTime->format('Y-m-d H:i:s');
                $isCurrentHour = $day->format('Y-m-d') === $currentDay && $hour === $currentHour;

                // Get tasks for this time slot and sort by start time
                $cellTasks = $tasks->filter(function($task) use ($cellDateTime) {
                    return $task->start_datetime->format('Y-m-d H') === $cellDateTime->format('Y-m-d H');
                })->sortBy('start_datetime');

                // Get teaching schedules for this time slot and sort by start time
                $cellSchedules = $teachingSchedules->filter(function($schedule) use ($cellDateTime) {
                    return $schedule->start_time->format('Y-m-d H') === $cellDateTime->format('Y-m-d H');
                })->sortBy('start_time');
            @endphp

            <div class="timetable-cell drop-zone relative {{ $isCurrentHour ? 'bg-blue-50 dark:bg-blue-900 ring-1 ring-blue-200 dark:ring-blue-700' : '' }}"
                 data-datetime="{{ $cellDateTimeString }}">

                <!-- Current time indicator -->
                @if($isCurrentHour)
                    <div class="current-time-indicator" style="top: {{ (now()->minute / 60) * 100 }}%"></div>
                @endif

                <!-- Tasks -->
                @foreach($cellTasks as $task)
                {{-- <div x-data="{showPopover:false,taskType:'task',
                taskID:@js($task->id),
                taskTitle:@js($task->title),
                taskLoc:@js($task->location),
                taskData:@js($task->load(['user', 'assignedUser']))}" class="task-item relative inline-block"> --}}
                <div class="task-item relative inline-block">
                    <div
                        class="draggable-item priority-{{ $task->priority }} cursor-pointer"
                        draggable="true"
                        data-id="{{ $task->id }}"
                        data-type="task"
                        {{-- title="{{ $task->title }} - {{ $task->time_range }}" --}}
                        {{-- onclick="showTaskPopover(event, {{ $task->id }}, 'task')" --}}
                        @click="showPopover=!showPopover"
                    >
                        <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                            {{ $task->title }}
                        </div>
                        <div class="text-gray-600 dark:text-gray-400 flex items-center gap-1 text-xs">
                            <span class="font-mono font-medium">{{ $task->start_datetime->format('H:i') }}</span>
                            @if($task->end_datetime)
                                <span class="text-gray-500">-{{ $task->end_datetime->format('H:i') }}</span>
                            @endif
                            @if($task->priority === 'high' || $task->priority === 'urgent')
                                ‼️{{-- 📌 --}} 
                                {{-- <span class="w-1.5 h-1.5 bg-red-500 rounded-full"></span> --}}
                            @endif
                            @if($task->has_alert)
                                🔔
                                {{-- <x-heroicon-o-bell class="w-2.5 h-2.5" /> --}}
                            @endif
                        </div>
                        @if($task->assignedUser)
                            <div class="text-gray-500 dark:text-gray-400 text-xs truncate">
                                Assigned to: {{ $task->assignedUser->name }}
                            </div>
                        @endif
                        @if($task->user && $task->assigned_to && $task->user_id !== $task->assigned_to)
                            <div class="text-gray-500 dark:text-gray-400 text-xs truncate">
                                By: {{ $task->user->name }}
                            </div>
                        @endif
                    </div>
                    {{-- @include("filament.resources.task-resource.pages.partials.popover") --}}
                </div>
                @endforeach

                <!-- Teaching Schedules -->
                @foreach($cellSchedules as $schedule) 
                <div class="task-item relative inline-block" 
                {{-- x-data="{showPopover:false,taskType:'schedule',
                taskID:@js($schedule->id),
                taskTitle:@js($schedule->subject->name),
                taskLoc:@js($schedule->classroom->room_name),
                taskData:@js($schedule)}"  --}}
                > 
                    <div
                        class="draggable-item schedule-item cursor-pointer"
                        draggable="true"
                        data-id="{{ $schedule->id }}"
                        data-type="schedule"
                        {{-- @click="showPopover=!showPopover" --}}
                        {{-- title="{{ $schedule->subject->name }} - {{ $schedule->classroom->room_name }}" --}}
                        {{-- onclick="showTaskPopover(event, {{ $schedule->id }}, 'schedule')" --}}
                    >
                        <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                            {{ $schedule->subject->name }}
                        </div>
                        <div class="text-gray-600 dark:text-gray-400 text-xs">
                            <span class="font-mono font-medium">{{ $schedule->start_time->format('H:i') }}</span>
                            @if($schedule->end_time)
                                <span class="text-gray-500">-{{ $schedule->end_time->format('H:i') }}</span>
                            @endif
                        </div>
                        <div class="text-gray-500 dark:text-gray-400 text-xs truncate">
                            ({{ $schedule->classroom->room_number }})
                            {{ $schedule->classroom->room_name }}
                        </div>
                    </div>
                    @include("filament.resources.task-resource.pages.partials.popover")
                </div>
                @endforeach

                <!-- Empty state for drop zone -->
                @if($cellTasks->isEmpty() && $cellSchedules->isEmpty())
                    <div class="h-full flex items-center justify-center text-gray-400 dark:text-gray-600 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 rounded group relative"
                         {{-- onclick="createTaskAtTime('{{ $cellDateTimeString }}')" --}}
                         oncontextmenu="showContextMenu(event, '{{ $cellDateTimeString }}')"
                         onclick="showContextMenu(event, '{{ $cellDateTimeString }}')"
                         title="Left click: Add Task | Right click: More options">
                        <div class="text-center">
                            <span class="text-xs opacity-0 group-hover:opacity-100 transition-opacity font-medium">+ Add Task</span>
                            <div class="text-xs opacity-0 group-hover:opacity-100 transition-opacity text-gray-500 dark:text-gray-400 mt-1">
                                {{ $cellDateTime->format('H:i') }}
                            </div>
                        </div>
                        <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <span class="text-xs">⋮</span>
                        </div>
                    </div>
                @endif
            </div>
        @endforeach
    @endforeach
</div>
