<?php
    $selectedMonth = now()->parse($selectedDate);
    $startOfMonth = $selectedMonth->copy()->startOfMonth()->startOfWeek();
    $endOfMonth = $selectedMonth->copy()->endOfMonth()->endOfWeek();
    $weeks = [];
    $current = $startOfMonth->copy();
    
    while ($current->lte($endOfMonth)) {
        $week = [];
        for ($i = 0; $i < 7; $i++) {
            $week[] = $current->copy();
            $current->addDay();
        }
        $weeks[] = $week;
    }
?>

<div class="max-w-6xl mx-auto">
    <!-- Month Header -->
    <div class="bg-gray-50 dark:bg-gray-700 p-4 text-center border-b">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            <?php echo e($selectedMonth->format('F Y')); ?>

        </h2>
    </div>

    <!-- Calendar Grid -->
    <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-600">
        <!-- Day Headers -->
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-gray-50 dark:bg-gray-700 p-2 text-center">
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100"><?php echo e($day); ?></span>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Calendar Days -->
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $weeks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $week): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $week; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $isCurrentMonth = $day->month === $selectedMonth->month;
                    $isToday = $day->isToday();
                    $dayDateString = $day->format('Y-m-d');
                    
                    // Get tasks for this day and sort by start time
                    $dayTasks = $tasks->filter(function($task) use ($day) {
                        return $task->start_datetime->format('Y-m-d') === $day->format('Y-m-d');
                    })->sortBy('start_datetime');

                    // Get teaching schedules for this day and sort by start time
                    $daySchedules = $teachingSchedules->filter(function($schedule) use ($day) {
                        return $schedule->start_time->format('Y-m-d') === $day->format('Y-m-d');
                    })->sortBy('start_time');
                ?>
                
                <div class="bg-white dark:bg-gray-800 min-h-[120px] p-2 drop-zone <?php echo e($isCurrentMonth ? '' : 'opacity-50'); ?> <?php echo e($isToday ? 'ring-2 ring-blue-500' : ''); ?>" 
                     data-datetime="<?php echo e($day->format('Y-m-d 09:00:00')); ?>">
                    
                    <!-- Day Number -->
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-sm font-medium <?php echo e($isToday ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-gray-100'); ?>">
                            <?php echo e($day->format('j')); ?>

                        </span>
                        <!--[if BLOCK]><![endif]--><?php if($dayTasks->count() + $daySchedules->count() > 3): ?>
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                +<?php echo e($dayTasks->count() + $daySchedules->count() - 3); ?> more
                            </span>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Tasks and Schedules -->
                    <div class="space-y-1">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $dayTasks->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div
                                class="draggable-item p-1 rounded text-xs cursor-pointer truncate"
                                style="background-color: <?php echo e($task->color); ?>20; border-left: 2px solid <?php echo e($task->color); ?>"
                                draggable="true"
                                data-id="<?php echo e($task->id); ?>"
                                data-type="task"
                                title="<?php echo e($task->title); ?> - <?php echo e($task->time_range); ?>"
                                onclick="showTaskPopover(event, <?php echo e($task->id); ?>, 'task')"
                            >
                                <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                                    <?php echo e($task->title); ?>

                                </div>
                                <div class="text-gray-600 dark:text-gray-400 flex items-center gap-1">
                                    <span><?php echo e($task->start_datetime->format('H:i')); ?></span>
                                    <!--[if BLOCK]><![endif]--><?php if($task->priority === 'high' || $task->priority === 'urgent'): ?>
                                        <span class="w-1.5 h-1.5 bg-red-500 rounded-full"></span>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($task->has_alert): ?>
                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-bell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-2 h-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $daySchedules->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div
                                class="draggable-item p-1 rounded text-xs cursor-pointer truncate"
                                style="background-color: #10B98120; border-left: 2px solid #10B981"
                                draggable="true"
                                data-id="<?php echo e($schedule->id); ?>"
                                data-type="schedule"
                                title="<?php echo e($schedule->subject->name); ?> - <?php echo e($schedule->classroom->room_name); ?>"
                                onclick="showTaskPopover(event, <?php echo e($schedule->id); ?>, 'schedule')"
                            >
                                <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                                    <?php echo e($schedule->subject->name); ?>

                                </div>
                                <div class="text-gray-600 dark:text-gray-400">
                                    <?php echo e($schedule->start_time->format('H:i')); ?>

                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Show remaining count if there are more items -->
                        <?php if($dayTasks->count() + $daySchedules->count() > 4): ?>
                            <div class="text-xs text-gray-500 dark:text-gray-400 text-center py-1">
                                <?php echo e($dayTasks->count() + $daySchedules->count() - 4); ?> more items
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Empty state for drop zone -->
                        <!--[if BLOCK]><![endif]--><?php if($dayTasks->isEmpty() && $daySchedules->isEmpty()): ?>
                            <div class="h-16 flex items-center justify-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors rounded"
                                 
                                 onclick="showContextMenu(event, '<?php echo e($day->format('Y-m-d')); ?> 09:00:00')"
                                 title="Click to create task on <?php echo e($day->format('M j')); ?>">
                                <span class="text-xs text-gray-400 dark:text-gray-600 opacity-0 hover:opacity-100 transition-opacity">
                                    + Add Task
                                </span>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Projects\Web\htdocs\edu-v2\resources\views/filament/resources/task-resource/pages/partials/month-view.blade.php ENDPATH**/ ?>