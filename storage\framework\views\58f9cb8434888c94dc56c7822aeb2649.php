<?php
    $selectedDay = now()->parse($selectedDate);
    $hours = range(6, 22); // 6 AM to 10 PM
    $currentHour = now()->hour;
    $isToday = $selectedDay->isToday();
?>

<div class="max-w-6xl mx-auto">
    <!-- Day Header -->
    <div class="bg-gray-50 dark:bg-gray-700 p-6 text-center border-b">
        <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 <?php echo e($isToday ? 'text-blue-600 dark:text-blue-400' : ''); ?>">
            <?php echo e($selectedDay->format('l, F j, Y')); ?>

            <!--[if BLOCK]><![endif]--><?php if($isToday): ?>
                <span class="text-sm font-normal text-blue-500 ml-2">(Today)</span>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </h2>
    </div>

    <!-- Time Slots -->
    <div class="divide-y divide-gray-200 dark:divide-gray-600">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $hours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $cellDateTime = $selectedDay->copy()->setHour($hour)->setMinute(0)->setSecond(0);
                $cellDateTimeString = $cellDateTime->format('Y-m-d H:i:s');
                $isCurrentHour = $isToday && $hour === $currentHour;

                // Get tasks for this time slot and sort by start time
                $cellTasks = $tasks->filter(function($task) use ($cellDateTime) {
                    return $task->start_datetime->format('Y-m-d H') === $cellDateTime->format('Y-m-d H');
                })->sortBy('start_datetime');

                // Get teaching schedules for this time slot and sort by start time
                $cellSchedules = $teachingSchedules->filter(function($schedule) use ($cellDateTime) {
                    return $schedule->start_time->format('Y-m-d H') === $cellDateTime->format('Y-m-d H');
                })->sortBy('start_time');
            ?>

            <div class="flex day-view-hour <?php echo e($isCurrentHour ? 'bg-blue-50 dark:bg-blue-900' : ''); ?>">
                <!-- Time Label -->
                <div class="w-24 flex-shrink-0 bg-gray-50 dark:bg-gray-700 p-4 text-center border-r">
                    <span class="text-sm font-medium <?php echo e($isCurrentHour ? 'text-blue-600 dark:text-blue-400 font-bold' : 'text-gray-900 dark:text-gray-100'); ?>">
                        <?php echo e(sprintf('%02d:00', $hour)); ?>

                    </span>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <?php echo e($cellDateTime->format('A')); ?>

                    </div>
                </div>

                <!-- Content Area -->
                <div class="flex-1 p-4 drop-zone min-h-[100px] relative" data-datetime="<?php echo e($cellDateTimeString); ?>">

                    <!-- Current time indicator -->
                    <!--[if BLOCK]><![endif]--><?php if($isCurrentHour): ?>
                        <div class="current-time-indicator" style="top: <?php echo e((now()->minute / 60) * 100); ?>%"></div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <div class="space-y-3">
                        <!-- Tasks -->
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cellTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div
                                class="draggable-item p-4 rounded-lg border-l-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                style="border-left-color: <?php echo e($task->priority_color); ?>; background-color: <?php echo e($task->color); ?>10"
                                draggable="true"
                                data-id="<?php echo e($task->id); ?>"
                                data-type="task"
                                onclick="showTaskPopover(event, <?php echo e($task->id); ?>, 'task')"
                            >
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-2 mb-2">
                                            <h4 class="font-semibold text-gray-900 dark:text-gray-100">
                                                <?php echo e($task->title); ?>

                                            </h4>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo e($task->priority === 'urgent' ? 'bg-red-100 text-red-800' : ($task->priority === 'high' ? 'bg-orange-100 text-orange-800' : ($task->priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'))); ?>">
                                                <?php echo e(ucfirst($task->priority)); ?>

                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                            <span class="font-mono font-medium"><?php echo e($task->time_range); ?></span>
                                            <!--[if BLOCK]><![endif]--><?php if($task->location): ?>
                                                <span class="ml-2">📍 <?php echo e($task->location); ?></span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </p>
                                        <!--[if BLOCK]><![endif]--><?php if($task->description): ?>
                                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                                                <?php echo e(Str::limit($task->description, 120)); ?>

                                            </p>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <!--[if BLOCK]><![endif]--><?php if($task->assignedUser): ?>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                👤 Assigned to: <?php echo e($task->assignedUser->name); ?>

                                            </p>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <!--[if BLOCK]><![endif]--><?php if($task->user && $task->assigned_to && $task->user_id !== $task->assigned_to): ?>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                📝 By: <?php echo e($task->user->name); ?>

                                            </p>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                    <div class="flex flex-col items-center gap-2 ml-4">
                                        <!--[if BLOCK]><![endif]--><?php if($task->has_alert): ?>
                                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-bell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 text-blue-500','title' => 'Has Alert']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <!--[if BLOCK]><![endif]--><?php if($task->is_recurring): ?>
                                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-arrow-path'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 text-purple-500','title' => 'Recurring']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <span class="px-2 py-1 text-xs rounded-full <?php echo e($task->status === 'completed' ? 'bg-green-100 text-green-800' : ($task->status === 'in_progress' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')); ?>">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $task->status))); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Teaching Schedules -->
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cellSchedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div
                                class="draggable-item p-4 rounded-lg border-l-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                style="border-left-color: #10B981; background-color: #10B98115"
                                draggable="true"
                                data-id="<?php echo e($schedule->id); ?>"
                                data-type="schedule"
                                onclick="showTaskPopover(event, <?php echo e($schedule->id); ?>, 'schedule')"
                            >
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-2 mb-2">
                                            <h4 class="font-semibold text-gray-900 dark:text-gray-100">
                                                📚 <?php echo e($schedule->subject->name); ?>

                                            </h4>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                                Class
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                            <span class="font-mono font-medium"><?php echo e($schedule->time_range); ?></span>
                                        </p>
                                        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                            <span>🏫 <?php echo e($schedule->classroom->room_name); ?></span>
                                            <span>👨‍🏫 <?php echo e($schedule->teacher->name); ?></span>
                                        </div>
                                        <!--[if BLOCK]><![endif]--><?php if($schedule->lesson): ?>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                Lesson: <?php echo e($schedule->lesson->title); ?>

                                            </p>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                    <div class="ml-4">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                                            <?php echo e(ucfirst($schedule->status)); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Empty state -->
                        <!--[if BLOCK]><![endif]--><?php if($cellTasks->isEmpty() && $cellSchedules->isEmpty()): ?>
                            <div class="h-20 flex items-center justify-center text-gray-400 dark:text-gray-600 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 cursor-pointer group relative"
                                 
                                 oncontextmenu="showContextMenu(event, '<?php echo e($cellDateTimeString); ?>')"
                                 onclick="showContextMenu(event, '<?php echo e($cellDateTimeString); ?>')"
                                 title="Left click: Add Task | Right click: More options">
                                <div class="text-center">
                                    <span class="text-sm group-hover:font-medium">Drop tasks here</span>
                                    <div class="text-xs mt-1 group-hover:font-medium">or click to add new</div>
                                </div>
                                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <span class="text-xs">⋮</span>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Projects\Web\htdocs\edu-v2\resources\views/filament/resources/task-resource/pages/partials/day-view.blade.php ENDPATH**/ ?>