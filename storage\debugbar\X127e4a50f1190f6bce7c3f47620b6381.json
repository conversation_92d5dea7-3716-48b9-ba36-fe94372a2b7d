{"__meta": {"id": "X127e4a50f1190f6bce7c3f47620b6381", "datetime": "2025-06-23 02:05:23", "utime": 1750644323.819357, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750644322.913985, "end": 1750644323.819382, "duration": 0.9053969383239746, "duration_str": "905ms", "measures": [{"label": "Booting", "start": 1750644322.913985, "relative_start": 0, "end": 1750644323.344889, "relative_end": 1750644323.344889, "duration": 0.43090391159057617, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750644323.344897, "relative_start": 0.4309120178222656, "end": 1750644323.819384, "relative_end": 2.1457672119140625e-06, "duration": 0.4744870662689209, "duration_str": "474ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49116784, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\HomepageController@index", "namespace": null, "prefix": "", "where": [], "as": "homepage", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FHttp%2FControllers%2FHomepageController.php&line=14\" onclick=\"\">app/Http/Controllers/HomepageController.php:14-17</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A9L7XeEUunzWMnNfFmKnDwxoAxciQY8uaff0xrnZ", "_previous": "array:1 [\n  \"url\" => \"http://d4.demo.com:81\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1741850668 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1741850668\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1616619064 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1616619064\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1668659007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1668659007\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1803076186 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">d4.demo.com:81</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,th;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"93 characters\">_ga=GA1.1.1214339266.1750644192; _ga_1TP47FSFJE=GS2.1.s1750644191$o1$g1$t1750644294$j60$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803076186\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-857150486 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_1TP47FSFJE</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857150486\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-999650447 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 23 Jun 2025 02:05:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjVJdXlzWEE5RjlGUHZyVDI0ZVcyVEE9PSIsInZhbHVlIjoiSitYM2tzczNJRCtFS0RNcmx2cDQ0c2xCblNld1M5OHZRWnpxTm9FTlhQOG9ZUU9oNFgycmZLaVZJSXc1MktjZ1B1WEI0MVREN2lBODNsWGJFZ1pERTVhZVBxL1B4eThNNFVOa0EyWi9qVGlueU45Zkl0SFBobnNXaHZqSCtabFEiLCJtYWMiOiIwNjllNjc1NmVmZWRlODQ2MzAzZTRkZDFiYmViYTk3NjYxYzkxOWJiNTE4MWFkNjYyMGIxNTMyNmZjYjQxMGNkIiwidGFnIjoiIn0%3D; expires=Mon, 23 Jun 2025 04:05:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6ImJRS0xjV2JremdMR1Mzakc4aENXeUE9PSIsInZhbHVlIjoiVVlaYWEyNFIxcTBFa3hNdVRubzJXY0RtOWZreGVod3FxeTgwanNzdEtYa2x5Ry9yd2praEhTWDNNZEh6cGhBSnZsdytEUjBvN01EYlU5REk4ZUhQTC91anJsNVJUQXMzbmRHWngrSFk2bWphMVowQ21GLzhuWWlmVFY1Mkd3dDAiLCJtYWMiOiI3N2VkYTNlNWYxNWMwZDQ0NDBiZWNhYzhiMmVlMGQ1ZmIzMTZkZmMzNDYwZjMzMjY4MzBlNjI5YjI0NGEwM2IyIiwidGFnIjoiIn0%3D; expires=Mon, 23 Jun 2025 04:05:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjVJdXlzWEE5RjlGUHZyVDI0ZVcyVEE9PSIsInZhbHVlIjoiSitYM2tzczNJRCtFS0RNcmx2cDQ0c2xCblNld1M5OHZRWnpxTm9FTlhQOG9ZUU9oNFgycmZLaVZJSXc1MktjZ1B1WEI0MVREN2lBODNsWGJFZ1pERTVhZVBxL1B4eThNNFVOa0EyWi9qVGlueU45Zkl0SFBobnNXaHZqSCtabFEiLCJtYWMiOiIwNjllNjc1NmVmZWRlODQ2MzAzZTRkZDFiYmViYTk3NjYxYzkxOWJiNTE4MWFkNjYyMGIxNTMyNmZjYjQxMGNkIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 04:05:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6ImJRS0xjV2JremdMR1Mzakc4aENXeUE9PSIsInZhbHVlIjoiVVlaYWEyNFIxcTBFa3hNdVRubzJXY0RtOWZreGVod3FxeTgwanNzdEtYa2x5Ry9yd2praEhTWDNNZEh6cGhBSnZsdytEUjBvN01EYlU5REk4ZUhQTC91anJsNVJUQXMzbmRHWngrSFk2bWphMVowQ21GLzhuWWlmVFY1Mkd3dDAiLCJtYWMiOiI3N2VkYTNlNWYxNWMwZDQ0NDBiZWNhYzhiMmVlMGQ1ZmIzMTZkZmMzNDYwZjMzMjY4MzBlNjI5YjI0NGEwM2IyIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 04:05:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999650447\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-769808587 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9L7XeEUunzWMnNfFmKnDwxoAxciQY8uaff0xrnZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://d4.demo.com:81</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769808587\", {\"maxDepth\":0})</script>\n"}}